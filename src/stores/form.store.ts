import { type FormByIdOutput, type FormField } from "@/types/form.types";
import { produce } from "immer";
import { create } from "zustand";

type FormStateStore = {
  form: FormByIdOutput | null;
  selectedId: string | null;
};

type FormActions = {
  // Maintains compatibility with existing callers
  setForm: (form: FormByIdOutput) => void;
  // Convenience load method (alias)
  loadForm: (form: FormByIdOutput) => void;

  // Selection
  select: (id: string | null) => void;

  // Field operations
  addField: (field: FormField, index?: number) => void;
  updateField: (id: string, updater: (f: FormField) => void) => void;
  removeField: (id: string) => void;
  moveField: (fromIndex: number, toIndex: number) => void;
};

export const useFormStore = create<FormStateStore & FormActions>()((set) => ({
  // State
  form: null,
  selectedId: null,

  // Mutators
  setForm: (form) => set({ form }),
  loadForm: (form) => set({ form }),
  select: (id) => set({ selectedId: id }),

  addField: (field, index) =>
    set(
      produce((state: FormStateStore & FormActions) => {
        if (!state.form) return;
        const i = index ?? state.form.fields.length;
        state.form.fields.splice(i, 0, field);
      }),
    ),

  updateField: (id, updater) =>
    set(
      produce((state: FormStateStore & FormActions) => {
        const target = state.form?.fields.find((f) => f.id === id);
        if (target) updater(target);
      }),
    ),

  removeField: (id) =>
    set(
      produce((state: FormStateStore & FormActions) => {
        if (!state.form) return;
        state.form.fields = state.form.fields.filter((f) => f.id !== id);
      }),
    ),

  moveField: (fromIndex, toIndex) =>
    set(
      produce((state: FormStateStore & FormActions) => {
        if (!state.form) return;
        if (fromIndex === toIndex) return;
        const list = state.form.fields;
        if (
          fromIndex < 0 ||
          toIndex < 0 ||
          fromIndex >= list.length ||
          toIndex > list.length
        )
          return;
        const [item] = list.splice(fromIndex, 1);
        if (!item) return; // guard: splice can return undefined
        list.splice(toIndex, 0, item);
      }),
    ),
}));
