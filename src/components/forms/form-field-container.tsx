import { useHover } from "@/hooks/use-hover";
import type { FormField } from "@/types/form.types";
import { cn } from "@/utils/tailwind-helpers";
import { useSortable } from "@dnd-kit/react/sortable";
import { IconGripHorizontal } from "@tabler/icons-react";
import { useEffect } from "react";
import { Card } from "../ui/card";
interface FormFieldContainerProps {
  children: React.ReactNode;
  fieldId: string | number;
  index?: number;
  selectedId?: string;
  setSelectedId: (fieldId: any) => void;
  element?: FormField;
}

export function FormFieldContainer({
  children,
  fieldId,
  index = 0,
  selectedId,
  setSelectedId,
  element,
}: FormFieldContainerProps) {
  const { hovered, ref } = useHover();

  const {
    ref: draggableRef,
    isDragging,
    isDropping,
    sortable,
  } = useSortable({ id: fieldId, index });

  const handleSelect = () => setSelectedId(fieldId);

  const isSelected = fieldId === selectedId;

  useEffect(() => {
    setSelectedId(sortable.id);
  }, [sortable, isDropping, setSelectedId]);

  return (
    <div ref={draggableRef} key={fieldId}>
      <div ref={ref}>
        {isSelected && (
          <div>
            <Card
              onClick={handleSelect}
              className={cn(
                "relative border-gray-600/20 p-5",
                isSelected && "border-gray-600/50 shadow-lg",
              )}
            >
              <div className="absolute -top-[2px] left-[48%] cursor-grab">
                <IconGripHorizontal className="text-gray-500" />
              </div>
              {children}
            </Card>
          </div>
        )}
        {!isSelected && (
          <div className="cursor-pointer">
            <Card
              onClick={handleSelect}
              className={cn(
                "relative cursor-pointer border-gray-600/20 p-5",
                isSelected && "border-gray-600/50 shadow-lg",
              )}
            >
              {(hovered || isDragging) && (
                <div className="absolute -top-[2px] left-[48%] cursor-grab">
                  <IconGripHorizontal className="text-gray-500" />
                </div>
              )}
              <div className="cursor-pointer">{children}</div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
