# Form Builder Simplification Plan

This plan focuses on dramatically simplifying the form builder codebase to make it easier to work with, maintain, and extend. The goal is to reduce complexity while keeping all existing functionality.

## Current Problems Identified

1. **Massive prop drilling** - Every element receives 6+ props (form, setForm, selectedId, setSelectedId, etc.)
2. **Duplicate code** - Each element has nearly identical logic for editing, duplicating, deleting
3. **Complex render function** - 1000+ line renderElement function with massive switch statement
4. **Mixed concerns** - Elements handle both rendering and state management
5. **No separation** - UI logic mixed with business logic throughout
6. **Manual state updates** - Every element manually updates form state with complex logic

## Simplification Goals

- **Eliminate prop drilling** - Use store selectors and actions instead
- **Remove duplicate code** - Extract common element behaviors
- **Simplify element components** - Focus only on rendering and field-specific logic
- **Clean separation** - Separate rendering from state management
- **Standardize patterns** - Consistent approach across all elements
- **Reduce complexity** - Make adding new elements trivial

---

## 1) Enhanced Store with Actions (src/stores/form.store.ts)

The store already has good actions, but we'll add a few more for common operations:

```ts
// Add these actions to existing store
export const useFormStore = create<FormStateStore & FormActions>()((set) => ({
  // ... existing state and actions ...

  // New convenience actions
  duplicateField: (id: string) =>
    set(
      produce((state) => {
        if (!state.form) return;
        const field = state.form.fields.find((f) => f.id === id);
        if (!field) return;

        const newField = { ...field, id: nanoid(8) };
        const index = state.form.fields.findIndex((f) => f.id === id);
        state.form.fields.splice(index + 1, 0, newField);
        state.selectedId = newField.id;
      }),
    ),

  updateFieldProperty: (id: string, property: keyof FormField, value: any) =>
    set(
      produce((state) => {
        const field = state.form?.fields.find((f) => f.id === id);
        if (field) field[property] = value;
      }),
    ),
}));
```

Benefits:

- Eliminates prop drilling completely
- Standardizes all field operations
- Simplifies element components

---

## 2) Common Element Behaviors Hook (src/hooks/use-field-actions.ts)

Extract all the common logic that every element currently duplicates:

```ts
export function useFieldActions(fieldId: string) {
  const { duplicateField, removeField, updateFieldProperty, select } =
    useFormStore();

  const duplicate = () => duplicateField(fieldId);
  const remove = () => removeField(fieldId);
  const updateProperty = (property: keyof FormField, value: any) =>
    updateFieldProperty(fieldId, property, value);
  const selectField = () => select(fieldId);

  return { duplicate, remove, updateProperty, selectField };
}
```

Benefits:

- Removes 50+ lines of duplicate code from each element
- Consistent behavior across all elements
- Single place to modify common actions

---

## 3) Simplified Element Components

Transform elements from complex state managers to simple renderers:

**Before (HeadingElement.tsx - 185 lines):**

- Manages local state
- Handles debouncing
- Implements duplicate/delete logic
- Updates form state manually

**After (HeadingElement.tsx - ~50 lines):**

```tsx
export function HeadingElement({ field }: { field: FormField }) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty } = useFieldActions(field.id);
  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer fieldId={field.id}>
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
        />
      ) : (
        <ViewMode field={field} />
      )}
    </FormFieldContainer>
  );
}
```

Benefits:

- 70% reduction in component size
- No prop drilling
- Focus only on rendering logic
- Consistent patterns

---

## 4) Shared Edit/View Components (src/components/forms/shared/)

Create reusable components for common element patterns:

```tsx
// EditMode.tsx - handles the editing UI for any field
export function EditMode({ field, onUpdate, onDuplicate, onDelete, children }) {
  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <FieldLabelInput
          value={field.label}
          onChange={(value) => onUpdate("label", value)}
        />
        {field.showDescription && (
          <FieldDescriptionInput
            value={field.description}
            onChange={(value) => onUpdate("description", value)}
          />
        )}
        {children} {/* Field-specific content */}
      </div>
      <FieldActions
        onDuplicate={onDuplicate}
        onDelete={onDelete}
        showDescription={field.showDescription}
        onToggleDescription={() =>
          onUpdate("showDescription", !field.showDescription)
        }
      />
    </div>
  );
}
```

Benefits:

- Consistent editing experience
- Shared validation and styling
- Reduces element code by 80%

---

## 5) Element Registry System (src/components/forms/elements/registry.ts)

Replace the massive renderElement function with a clean registry:

```ts
type ElementDefinition = {
  type: string;
  label: string;
  icon: ReactNode;
  defaultField: () => FormField;
  Component: ComponentType<{ field: FormField }>;
};

const registry = new Map<string, ElementDefinition>();

export const registerElement = (def: ElementDefinition) => registry.set(def.type, def);
export const getElement = (type: string) => registry.get(type);
export const getAllElements = () => Array.from(registry.values());

// Register elements
registerElement({
  type: 'heading',
  label: 'Heading',
  icon: <IconHeading />,
  defaultField: () => ({ /* default heading field */ }),
  Component: HeadingElement,
});
```

Benefits:

- Eliminates 1000+ line renderElement function
- Adding new elements requires only registration
- Type-safe and extensible

---

## 6) Simplified Form Builder View

Transform the main builder from 1500+ lines to ~200 lines:

```tsx
export function FormBuildView({ formId, orgId }: Props) {
  const { form, selectedId } = useFormStore();

  return (
    <div className="form-builder">
      <ElementPalette />
      <FormCanvas>
        {form?.fields.map((field) => {
          const ElementComponent = getElement(field.subtype)?.Component;
          return ElementComponent ? (
            <ElementComponent key={field.id} field={field} />
          ) : null;
        })}
      </FormCanvas>
      <SettingsPanel />
    </div>
  );
}
```

Benefits:

- 90% reduction in component size
- Clear separation of concerns
- Easy to understand and modify

---

## 7) Implementation Steps

### Week 1: Foundation

1. Add convenience actions to form store
2. Create `useFieldActions` hook
3. Create shared `EditMode` and `ViewMode` components
4. Refactor 1-2 simple elements (heading, short_answer)

### Week 2: Element Migration

1. Create element registry system
2. Migrate remaining elements to new pattern
3. Replace renderElement function with registry lookup
4. Test all functionality works

### Week 3: Polish & Cleanup

1. Remove old element files
2. Add autosave hook (if needed)
3. Extract constants and utilities
4. Add tests for new patterns

---

## 8) Expected Results

- **90% reduction** in element component size
- **Zero prop drilling** - all state via hooks
- **Consistent patterns** - same approach everywhere
- **Easy extensibility** - new elements in minutes
- **Better maintainability** - clear separation of concerns
- **Preserved functionality** - everything works exactly the same

This plan prioritizes simplicity above all else while maintaining full functionality.

---

## 9) File Structure Changes

**New files to create:**

```
src/hooks/use-field-actions.ts          # Common field operations
src/components/forms/shared/EditMode.tsx        # Shared editing UI
src/components/forms/shared/ViewMode.tsx        # Shared viewing UI
src/components/forms/shared/FieldActions.tsx    # Duplicate/delete buttons
src/components/forms/shared/FieldInputs.tsx     # Label/description inputs
src/components/forms/elements/registry.ts       # Element registry
src/utils/form-defaults.ts                      # Default field creators
```

**Files to simplify:**

- All element components (90% size reduction)
- `form-build-view-v2.tsx` (90% size reduction)
- `form-field-container.tsx` (remove prop drilling)

---

## 10) Code Examples

### Before: HeadingElement (185 lines)

```tsx
export function HeadingElement({
  element,
  index,
  selectedId,
  setSelectedId,
  form,
  setForm,
}: Props) {
  const [newId, setNewId] = useState(selectedId);
  const [localLabel, setLocalLabel] = useState(element.label);
  const [localDescription, setLocalDescription] = useState(element.description);

  const debouncedSetForm = useMemo(
    () => debounce((updatedForm: FormByIdOutput) => setForm(updatedForm), 300),
    [setForm],
  );

  const updateLabelOrDescription = (e: React.SyntheticEvent) => {
    // 30+ lines of complex state update logic
  };

  const duplicateElement = () => {
    // 20+ lines of duplication logic
  };

  const deleteElement = () => {
    // 15+ lines of deletion logic
  };

  // 100+ more lines of JSX and logic...
}
```

### After: HeadingElement (45 lines)

```tsx
export function HeadingElement({ field }: { field: FormField }) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty } = useFieldActions(field.id);
  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer fieldId={field.id}>
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
        >
          {/* Heading-specific content if any */}
        </EditMode>
      ) : (
        <ViewMode field={field}>
          <h2 className="text-xl font-semibold">{field.label}</h2>
          {field.showDescription && <p>{field.description}</p>}
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
```

---

## 11) Benefits Summary

### Developer Experience

- **Faster development** - New elements in 10 minutes vs 2 hours
- **Less bugs** - Shared logic means fewer places for errors
- **Easier debugging** - Clear data flow and separation
- **Better testing** - Isolated, focused components

### Code Quality

- **90% less duplication** - Shared behaviors extracted
- **Consistent patterns** - Same approach everywhere
- **Type safety** - Registry ensures correct usage
- **Maintainability** - Changes in one place affect all elements

### Performance

- **Smaller bundle** - Less duplicate code
- **Better memoization** - Cleaner component boundaries
- **Optimized re-renders** - Precise state subscriptions

---

## 12) Migration Strategy

### Phase 1: Preparation (2 days)

1. Create shared components and hooks
2. Add registry system
3. Test with one simple element

### Phase 2: Element Migration (3 days)

1. Migrate 2-3 elements per day
2. Test each migration thoroughly
3. Keep old and new side by side

### Phase 3: Cleanup (1 day)

1. Remove old renderElement function
2. Delete old element files
3. Update imports and references

### Phase 4: Polish (1 day)

1. Add missing features if any
2. Performance optimizations
3. Documentation updates

---

## 13) Risk Mitigation

**Risk: Breaking existing functionality**

- Mitigation: Migrate one element at a time, thorough testing

**Risk: Performance regression**

- Mitigation: Use React.memo and proper memoization

**Risk: Type safety issues**

- Mitigation: Strong typing in registry and hooks

**Risk: Team adoption**

- Mitigation: Clear documentation and examples

---

## 14) Success Metrics

- [ ] Element components under 50 lines each
- [ ] Zero prop drilling in element tree
- [ ] New element creation under 10 minutes
- [ ] Main builder view under 200 lines
- [ ] All existing functionality preserved
- [ ] Performance maintained or improved

This simplified approach will make the form builder much more maintainable and extensible while keeping the same user experience.
